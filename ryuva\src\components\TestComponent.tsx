import React from 'react';

const TestComponent: React.FC = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-ryuva-cream">
      <div className="text-center p-8">
        <h1 className="text-4xl font-serif font-bold text-ryuva-charcoal mb-4">
          Ryuva Test Page
        </h1>
        <p className="text-lg text-gray-600 mb-6">
          If you can see this, <PERSON><PERSON> and <PERSON><PERSON><PERSON> are working correctly!
        </p>
        <div className="bg-ryuva-gold text-white px-6 py-3 rounded-lg inline-block">
          Test Button
        </div>
      </div>
    </div>
  );
};

export default TestComponent;
