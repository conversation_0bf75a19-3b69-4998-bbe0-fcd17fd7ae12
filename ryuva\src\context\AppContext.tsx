import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { apiService } from '../services/api';

// Define types locally to avoid import issues
interface User {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
}

interface Product {
  id: number;
  name: string;
  description: string;
  shortDescription: string;
  sku: string;
  category: string;
  subcategory: string;
  price: number;
  comparePrice?: number;
  images: Array<{
    url: string;
    alt: string;
    isPrimary: boolean;
  }>;
  fabric: {
    type: string;
    care: string[];
    origin: string;
  };
  colors: Array<{
    name: string;
    hex: string;
  }>;
  patterns: string[];
  tags: string[];
  inventory: {
    quantity: number;
    stockStatus: string;
  };
  features: string[];
  isCustomizable: boolean;
  customizationOptions?: {
    monogram?: {
      enabled: boolean;
      price: number;
      positions: string[];
      fonts: string[];
      colors: string[];
    };
  };
  ratings: {
    average: number;
    count: number;
  };
  isFeatured: boolean;
  discountPercentage: number;
}

interface CartItem {
  id: number;
  productId: number;
  quantity: number;
  customization?: {
    type: string;
    details?: any;
    additionalPrice: number;
  };
  product: Product;
  subtotal: number;
}

interface Cart {
  items: CartItem[];
  totalItems: number;
  subtotal: number;
  total: number;
}

interface AppState {
  user: User | null;
  cart: Cart | null;
  wishlist: Product[];
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
}

interface AppActions {
  login: (email: string, password: string) => Promise<any>;
  logout: () => void;
  loadUserData: () => Promise<void>;
  addToCart: (productId: number, quantity?: number, customization?: any) => Promise<void>;
  removeFromCart: (itemId: number) => Promise<void>;
  addToWishlist: (productId: number) => Promise<void>;
  removeFromWishlist: (productId: number) => Promise<void>;
  clearError: () => void;
}

const initialState: AppState = {
  user: null,
  cart: null,
  wishlist: [],
  isAuthenticated: false,
  loading: true,
  error: null,
};

type AppAction =
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_CART'; payload: Cart | null }
  | { type: 'SET_WISHLIST'; payload: Product[] }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'CLEAR_ERROR' };

const AppContext = createContext<{
  state: AppState;
  actions: AppActions;
}>({
  state: initialState,
  actions: {} as AppActions,
});

function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_USER':
      return { 
        ...state, 
        user: action.payload, 
        isAuthenticated: !!action.payload 
      };
    case 'SET_CART':
      return { ...state, cart: action.payload };
    case 'SET_WISHLIST':
      return { ...state, wishlist: action.payload };
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    default:
      return state;
  }
}

export function AppProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  const actions: AppActions = {
    async login(email: string, password: string) {
      try {
        dispatch({ type: 'SET_ERROR', payload: null });
        dispatch({ type: 'SET_LOADING', payload: true });
        
        const response = await apiService.login(email, password);
        dispatch({ type: 'SET_USER', payload: response.data.user });
        
        // Load user data after login
        await actions.loadUserData();
        
        return response;
      } catch (error: any) {
        dispatch({ type: 'SET_ERROR', payload: error.message });
        throw error;
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    logout() {
      apiService.logout();
      dispatch({ type: 'SET_USER', payload: null });
      dispatch({ type: 'SET_CART', payload: null });
      dispatch({ type: 'SET_WISHLIST', payload: [] });
    },

    async loadUserData() {
      try {
        console.log('AppContext: Starting loadUserData...');
        dispatch({ type: 'SET_LOADING', payload: true });

        const token = localStorage.getItem('authToken');
        if (!token) {
          console.log('AppContext: No token found, skipping user data load');
          dispatch({ type: 'SET_LOADING', payload: false });
          return;
        }

        console.log('AppContext: Making API calls...');
        const [userResponse, cartResponse, wishlistResponse] = await Promise.allSettled([
          apiService.getCurrentUser(),
          apiService.getCart(),
          apiService.getWishlist(),
        ]);

        console.log('AppContext: API responses received', {
          user: userResponse.status,
          cart: cartResponse.status,
          wishlist: wishlistResponse.status
        });

        if (userResponse.status === 'fulfilled') {
          dispatch({ type: 'SET_USER', payload: userResponse.value.data.user });
        } else {
          console.warn('AppContext: Failed to load user:', userResponse.reason);
        }

        if (cartResponse.status === 'fulfilled') {
          dispatch({ type: 'SET_CART', payload: cartResponse.value.data.cart });
        } else {
          console.warn('AppContext: Failed to load cart:', cartResponse.reason);
        }

        if (wishlistResponse.status === 'fulfilled') {
          dispatch({ type: 'SET_WISHLIST', payload: wishlistResponse.value.data.wishlist });
        } else {
          console.warn('AppContext: Failed to load wishlist:', wishlistResponse.reason);
        }
      } catch (error: any) {
        console.error('AppContext: Failed to load user data:', error);
        dispatch({ type: 'SET_ERROR', payload: 'Failed to load user data' });
      } finally {
        console.log('AppContext: Finished loadUserData, setting loading to false');
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    async addToCart(productId: number, quantity: number = 1, customization?: any) {
      try {
        dispatch({ type: 'SET_ERROR', payload: null });
        await apiService.addToCart(productId, quantity, customization);
        
        // Reload cart data
        const cartResponse = await apiService.getCart();
        dispatch({ type: 'SET_CART', payload: cartResponse.data.cart });
      } catch (error: any) {
        dispatch({ type: 'SET_ERROR', payload: error.message });
        throw error;
      }
    },

    async removeFromCart(itemId: number) {
      try {
        dispatch({ type: 'SET_ERROR', payload: null });
        await apiService.removeFromCart(itemId);
        
        // Reload cart data
        const cartResponse = await apiService.getCart();
        dispatch({ type: 'SET_CART', payload: cartResponse.data.cart });
      } catch (error: any) {
        dispatch({ type: 'SET_ERROR', payload: error.message });
        throw error;
      }
    },

    async addToWishlist(productId: number) {
      try {
        dispatch({ type: 'SET_ERROR', payload: null });
        await apiService.addToWishlist(productId);
        
        // Reload wishlist data
        const wishlistResponse = await apiService.getWishlist();
        dispatch({ type: 'SET_WISHLIST', payload: wishlistResponse.data.wishlist });
      } catch (error: any) {
        dispatch({ type: 'SET_ERROR', payload: error.message });
        throw error;
      }
    },

    async removeFromWishlist(productId: number) {
      try {
        dispatch({ type: 'SET_ERROR', payload: null });
        await apiService.removeFromWishlist(productId);
        
        // Reload wishlist data
        const wishlistResponse = await apiService.getWishlist();
        dispatch({ type: 'SET_WISHLIST', payload: wishlistResponse.data.wishlist });
      } catch (error: any) {
        dispatch({ type: 'SET_ERROR', payload: error.message });
        throw error;
      }
    },

    clearError() {
      dispatch({ type: 'CLEAR_ERROR' });
    },
  };

  useEffect(() => {
    console.log('AppContext: Initializing...');
    const token = localStorage.getItem('authToken');
    console.log('AppContext: Token found:', !!token);

    if (token) {
      console.log('AppContext: Loading user data...');
      actions.loadUserData();
    } else {
      console.log('AppContext: No token, setting loading to false');
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, []);

  return (
    <AppContext.Provider value={{ state, actions }}>
      {children}
    </AppContext.Provider>
  );
}

export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};
