import { Product, User, Cart, CartItem } from '../types';

const API_BASE_URL = 'http://localhost:3000/api';

class ApiService {
  private baseURL: string;
  private token: string | null = null;

  constructor() {
    this.baseURL = API_BASE_URL;
    this.token = localStorage.getItem('authToken');
  }

  private async request(endpoint: string, options: RequestInit = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'API request failed');
      }

      return data;
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }

  // Auth methods
  async login(email: string, password: string) {
    const data = await this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
    
    this.token = data.data.token;
    localStorage.setItem('authToken', this.token!);
    return data;
  }

  async getCurrentUser() {
    return this.request('/auth/me');
  }

  logout() {
    this.token = null;
    localStorage.removeItem('authToken');
  }

  // Product methods
  async getProducts(params?: {
    page?: number;
    limit?: number;
    category?: string;
    search?: string;
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }
    const queryString = queryParams.toString();
    return this.request(`/products${queryString ? `?${queryString}` : ''}`);
  }

  async getProduct(id: number) {
    return this.request(`/products/${id}`);
  }

  async getFeaturedProducts() {
    return this.request('/products/featured');
  }

  async getProductsByCategory(category: string) {
    return this.request(`/products/category/${category}`);
  }

  // Customization methods
  async getCustomizationOptions(productId: number) {
    return this.request(`/customization/${productId}`);
  }

  async calculateCustomizationPrice(productId: number, customizations: any[]) {
    return this.request(`/customization/${productId}/calculate`, {
      method: 'POST',
      body: JSON.stringify({ customizations }),
    });
  }

  // Cart methods
  async getCart() {
    return this.request('/cart');
  }

  async addToCart(productId: number, quantity: number = 1, customization?: any) {
    return this.request('/cart/add', {
      method: 'POST',
      body: JSON.stringify({ productId, quantity, customization }),
    });
  }

  async removeFromCart(itemId: number) {
    return this.request(`/cart/item/${itemId}`, {
      method: 'DELETE',
    });
  }

  // Wishlist methods
  async getWishlist() {
    return this.request('/wishlist');
  }

  async addToWishlist(productId: number) {
    return this.request(`/wishlist/${productId}`, {
      method: 'POST',
    });
  }

  async removeFromWishlist(productId: number) {
    return this.request(`/wishlist/${productId}`, {
      method: 'DELETE',
    });
  }

  // Order methods
  async createOrder(orderData: any) {
    return this.request('/orders', {
      method: 'POST',
      body: JSON.stringify(orderData),
    });
  }

  async getOrders() {
    return this.request('/orders');
  }

  async getOrder(id: number) {
    return this.request(`/orders/${id}`);
  }
}

export const apiService = new ApiService();

// Re-export types for backward compatibility
export type { Product, User, Cart, CartItem } from '../types';
