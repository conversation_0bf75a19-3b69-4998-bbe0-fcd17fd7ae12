import './App.css'
import Navbar from './components/Navbar'
import Home from './pages/Home'
import Products from './pages/Products'
import ErrorBoundary from './components/ErrorBoundary'
import { BrowserRouter, Routes, Route } from 'react-router-dom'
import { useScrollAnimation } from './hooks/useScrollAnimation'
import { AppProvider, useApp } from './context/AppContext'
import { ToastProvider } from './context/ToastContext'

function AppContent() {
  const { state } = useApp();
  useScrollAnimation();

  if (state.loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-ryuva-cream">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-ryuva-gold mx-auto mb-4"></div>
          <p className="text-ryuva-charcoal">Loading Ryuva...</p>
        </div>
      </div>
    );
  }

  return (
    <BrowserRouter>
      <Navbar/>
      <Routes>
        <Route path='/' element={<Home/>}/>
        <Route path='/products' element={<Products/>}/>
      </Routes>
    </BrowserRouter>
  );
}

function App() {
  return (
    <ErrorBoundary>
      <ToastProvider>
        <AppProvider>
          <AppContent />
        </AppProvider>
      </ToastProvider>
    </ErrorBoundary>
  )
}

export default App
