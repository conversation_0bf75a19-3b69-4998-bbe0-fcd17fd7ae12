import Product from '../models/Product.js';
import { asyncHandler } from '../middleware/errorHandler.js';

// @desc    Get customization options for a product
// @route   GET /api/customization/:productId
// @access  Public
export const getCustomizationOptions = asyncHandler(async (req, res) => {
  const product = await Product.findOne({
    _id: req.params.productId,
    isActive: true,
    isCustomizable: true
  }).select('name customizationOptions price');

  if (!product) {
    return res.status(404).json({
      success: false,
      message: 'Product not found or not customizable'
    });
  }

  res.json({
    success: true,
    data: {
      productId: product._id,
      productName: product.name,
      basePrice: product.price,
      customizationOptions: product.customizationOptions
    }
  });
});

// @desc    Calculate customization price
// @route   POST /api/customization/:productId/calculate
// @access  Public
export const calculateCustomizationPrice = asyncHandler(async (req, res) => {
  const { customizations } = req.body;

  const product = await Product.findOne({
    _id: req.params.productId,
    isActive: true,
    isCustomizable: true
  });

  if (!product) {
    return res.status(404).json({
      success: false,
      message: 'Product not found or not customizable'
    });
  }

  let totalCustomizationPrice = 0;
  const breakdown = [];

  for (const customization of customizations) {
    let price = 0;
    let description = '';

    switch (customization.type) {
      case 'monogram':
        if (product.customizationOptions.monogram?.enabled) {
          price = product.customizationOptions.monogram.price;
          description = `Monogram: "${customization.details.text}" in ${customization.details.font} font`;
        }
        break;

      case 'embroidery':
        if (product.customizationOptions.embroidery?.enabled) {
          price = product.customizationOptions.embroidery.price;
          description = `Custom embroidery: ${customization.details.design}`;
        }
        break;

      case 'fabric-upgrade':
        if (product.customizationOptions.fabricUpgrade?.enabled) {
          const fabricOption = product.customizationOptions.fabricUpgrade.options.find(
            option => option.fabric === customization.details.upgradedFabric
          );
          if (fabricOption) {
            price = fabricOption.additionalPrice;
            description = `Fabric upgrade to ${customization.details.upgradedFabric}`;
          }
        }
        break;

      default:
        continue;
    }

    totalCustomizationPrice += price;
    breakdown.push({
      type: customization.type,
      description,
      price
    });
  }

  const finalPrice = product.price + totalCustomizationPrice;

  res.json({
    success: true,
    data: {
      basePrice: product.price,
      customizationPrice: totalCustomizationPrice,
      finalPrice,
      breakdown,
      estimatedDelivery: calculateEstimatedDelivery(customizations)
    }
  });
});

// @desc    Validate customization details
// @route   POST /api/customization/:productId/validate
// @access  Public
export const validateCustomization = asyncHandler(async (req, res) => {
  const { customization } = req.body;

  const product = await Product.findOne({
    _id: req.params.productId,
    isActive: true,
    isCustomizable: true
  });

  if (!product) {
    return res.status(404).json({
      success: false,
      message: 'Product not found or not customizable'
    });
  }

  const validationErrors = [];

  switch (customization.type) {
    case 'monogram':
      if (!product.customizationOptions.monogram?.enabled) {
        validationErrors.push('Monogram customization not available for this product');
      } else {
        const { text, font, color, position } = customization.details;
        
        if (!text || text.length === 0) {
          validationErrors.push('Monogram text is required');
        } else if (text.length > 10) {
          validationErrors.push('Monogram text cannot exceed 10 characters');
        }
        
        if (!font || !product.customizationOptions.monogram.fonts.includes(font)) {
          validationErrors.push('Invalid font selection');
        }
        
        if (!color || !product.customizationOptions.monogram.colors.includes(color)) {
          validationErrors.push('Invalid color selection');
        }
        
        if (!position || !product.customizationOptions.monogram.positions.includes(position)) {
          validationErrors.push('Invalid position selection');
        }
      }
      break;

    case 'embroidery':
      if (!product.customizationOptions.embroidery?.enabled) {
        validationErrors.push('Embroidery customization not available for this product');
      } else {
        const { design, description } = customization.details;
        
        if (!design || !product.customizationOptions.embroidery.designs.includes(design)) {
          validationErrors.push('Invalid embroidery design selection');
        }
        
        if (description && description.length > 500) {
          validationErrors.push('Description cannot exceed 500 characters');
        }
      }
      break;

    case 'fabric-upgrade':
      if (!product.customizationOptions.fabricUpgrade?.enabled) {
        validationErrors.push('Fabric upgrade not available for this product');
      } else {
        const { upgradedFabric } = customization.details;
        
        const validFabrics = product.customizationOptions.fabricUpgrade.options.map(opt => opt.fabric);
        if (!upgradedFabric || !validFabrics.includes(upgradedFabric)) {
          validationErrors.push('Invalid fabric upgrade selection');
        }
      }
      break;

    default:
      validationErrors.push('Invalid customization type');
  }

  const isValid = validationErrors.length === 0;

  res.json({
    success: true,
    data: {
      isValid,
      errors: validationErrors,
      ...(isValid && {
        estimatedDelivery: calculateEstimatedDelivery([customization]),
        additionalPrice: calculateSingleCustomizationPrice(product, customization)
      })
    }
  });
});

// @desc    Get popular customization combinations
// @route   GET /api/customization/popular
// @access  Public
export const getPopularCustomizations = asyncHandler(async (req, res) => {
  // In a real application, this would query order data to find popular combinations
  const popularCombinations = [
    {
      id: 1,
      name: "Classic Monogram",
      description: "Traditional script monogram in gold",
      customizations: [
        {
          type: "monogram",
          details: {
            font: "Script",
            color: "Gold",
            position: "bottom-right"
          }
        }
      ],
      additionalPrice: 150,
      popularity: 85
    },
    {
      id: 2,
      name: "Wedding Special",
      description: "Elegant monogram with premium silk upgrade",
      customizations: [
        {
          type: "monogram",
          details: {
            font: "Elegant Script",
            color: "Silver",
            position: "center"
          }
        },
        {
          type: "fabric-upgrade",
          details: {
            upgradedFabric: "Premium Silk"
          }
        }
      ],
      additionalPrice: 450,
      popularity: 72
    },
    {
      id: 3,
      name: "Modern Minimalist",
      description: "Clean block monogram with linen upgrade",
      customizations: [
        {
          type: "monogram",
          details: {
            font: "Modern",
            color: "Navy",
            position: "bottom-right"
          }
        },
        {
          type: "fabric-upgrade",
          details: {
            upgradedFabric: "Premium Linen"
          }
        }
      ],
      additionalPrice: 300,
      popularity: 68
    }
  ];

  res.json({
    success: true,
    data: { popularCombinations }
  });
});

// Helper function to calculate estimated delivery time
const calculateEstimatedDelivery = (customizations) => {
  let baseDays = 3; // Standard delivery
  
  for (const customization of customizations) {
    switch (customization.type) {
      case 'monogram':
        baseDays += 2;
        break;
      case 'embroidery':
        baseDays += 5;
        break;
      case 'fabric-upgrade':
        baseDays += 3;
        break;
    }
  }
  
  const deliveryDate = new Date();
  deliveryDate.setDate(deliveryDate.getDate() + baseDays);
  
  return {
    days: baseDays,
    date: deliveryDate.toISOString().split('T')[0]
  };
};

// Helper function to calculate customization price
const calculateSingleCustomizationPrice = (product, customization) => {
  switch (customization.type) {
    case 'monogram':
      return product.customizationOptions.monogram?.price || 0;
    case 'embroidery':
      return product.customizationOptions.embroidery?.price || 0;
    case 'fabric-upgrade':
      const fabricOption = product.customizationOptions.fabricUpgrade?.options?.find(
        option => option.fabric === customization.details?.upgradedFabric
      );
      return fabricOption?.additionalPrice || 0;
    default:
      return 0;
  }
};
