export interface Product {
  id: number;
  name: string;
  description: string;
  shortDescription: string;
  sku: string;
  category: string;
  subcategory: string;
  price: number;
  comparePrice?: number;
  images: Array<{
    url: string;
    alt: string;
    isPrimary: boolean;
  }>;
  fabric: {
    type: string;
    care: string[];
    origin: string;
  };
  colors: Array<{
    name: string;
    hex: string;
  }>;
  patterns: string[];
  tags: string[];
  inventory: {
    quantity: number;
    stockStatus: string;
  };
  features: string[];
  isCustomizable: boolean;
  customizationOptions?: {
    monogram?: {
      enabled: boolean;
      price: number;
      positions: string[];
      fonts: string[];
      colors: string[];
    };
  };
  ratings: {
    average: number;
    count: number;
  };
  isFeatured: boolean;
  discountPercentage: number;
}

export interface User {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
}

export interface CartItem {
  id: number;
  productId: number;
  quantity: number;
  customization?: {
    type: string;
    details?: any;
    additionalPrice: number;
  };
  product: Product;
  subtotal: number;
}

export interface Cart {
  items: CartItem[];
  totalItems: number;
  subtotal: number;
  total: number;
}
